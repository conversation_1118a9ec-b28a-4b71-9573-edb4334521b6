
from openai import OpenAI
import os
from dotenv import load_dotenv
import requests
import base64
load_dotenv()
api_key=os.getenv("api_key")
api_url=os.getenv("api_url")
client=OpenAI(api_key=api_key,base_url=api_url)
url_1 = "https://cdn.openai.com/API/docs/audio/alloy.wav"
response = requests.get(url_1)
response.raise_for_status()
wav_data = response.content
encoded_string = base64.b64encode(wav_data).decode('utf-8')

completion = client.chat.completions.create(
    model="gpt-4o-audio-preview",
    modalities=["text", "audio"],
    audio={"voice": "alloy", "format": "wav"},
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "What is in this recording?"
                },
                {
                    "type": "input_audio",
                    "input_audio": {
                        "data": encoded_string,
                        "format": "wav"
                    }
                }
            ]
        },
    ]
)

print(completion.choices[0].message)
